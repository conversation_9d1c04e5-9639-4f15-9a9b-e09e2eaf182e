// export default ResturantForm;
import Heading from '@/components/shared/heading';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useCreateResturant } from '../../hooks/useCreateResturant';
import { useUpdateRestaurant } from '../../hooks/useUpdateRestaurant';
import toast from 'react-hot-toast';
import { Resturant } from '../../lib/types';
import { useEffect, useState } from 'react';
import { X } from 'lucide-react';
import { useUploadImage } from '@/hooks/useUploadImage';
import { Switch } from '@/components/ui/switch';
import { useFetchCountries } from '@/hooks/useFetchCountries';
import { useFetchCities } from '@/hooks/useFetchCities';
import { useFetchAreas } from '@/hooks/useFetchAreas';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LocationPicker } from '../LocationPicker';
import { useTranslation } from 'react-i18next';
import {
  MapPin,
  Building2,
  Globe,
  Phone,
  Mail,
  Clock,
  Image as ImageIcon,
  Share2,
  Camera,
  ToggleLeft
} from 'lucide-react';

const restaurantFormSchema = z.object({
  country_id: z.string().min(1, { message: 'Country is required' }),
  city_id: z.string().min(1, { message: 'City is required' }),
  area_id: z.string().min(1, { message: 'Area is required' }),
  address_en: z.string().min(1, { message: 'English address is required' }),
  address_ar: z.string().min(1, { message: 'Arabic address is required' }),
  address_tr: z.string().min(1, { message: 'Turkish address is required' }),
  name_en: z.string().min(1, { message: 'English name is required' }),
  name_ar: z.string().min(1, { message: 'Arabic name is required' }),
  name_tr: z.string().min(1, { message: 'Turkish name is required' }),
  email: z.string().email({ message: 'Invalid email address' }),
  phone: z.string().min(1, { message: 'Phone is required' }),
  logo: z.string().optional(),
  latitude: z.string().min(1, { message: 'Latitude is required' }),
  longitude: z.string().min(1, { message: 'Longitude is required' }),
  facebook_url: z
    .string()
    .url({ message: 'Invalid URL' })
    .optional()
    .or(z.literal('')),
  instagram_url: z
    .string()
    .url({ message: 'Invalid URL' })
    .optional()
    .or(z.literal('')),
  contact_number: z.string().min(1, { message: 'Contact number is required' }),
  is_available: z.boolean(),
  start_time: z.string().min(1, { message: 'Start time is required' }),
  end_time: z.string().min(1, { message: 'End time is required' })
});

type RestaurantFormValues = z.infer<typeof restaurantFormSchema>;

interface RestaurantFormProps {
  modalClose: () => void;
  resturant?: Resturant;
}

const RestaurantForm = ({ modalClose, resturant }: RestaurantFormProps) => {
  // Use i18next for translations
  const { t } = useTranslation();

  // Log the restaurant data for debugging
  console.log('Restaurant data received:', resturant);
  const form = useForm<RestaurantFormValues>({
    resolver: zodResolver(restaurantFormSchema),
    defaultValues: resturant
      ? {
          country_id: resturant.country_id
            ? resturant.country_id.toString()
            : '',
          city_id: resturant.city_id ? resturant.city_id.toString() : '',
          area_id: resturant.area_id ? resturant.area_id.toString() : '',
          address_en: resturant.address_en || '',
          address_ar: resturant.address_ar || '',
          address_tr: resturant.address_tr || '',
          name_en: resturant.name_en || '',
          name_ar: resturant.name_ar || '',
          name_tr: resturant.name_tr || '',
          email: resturant.email || '',
          phone: resturant.phone || '',
          logo:
            typeof resturant.logo === 'object'
              ? resturant.logo?.path || ''
              : resturant.logo || '',
          latitude: resturant.latitude || '',
          longitude: resturant.longitude || '',
          facebook_url: resturant.facebook_url || '',
          instagram_url: resturant.instagram_url || '',
          contact_number: resturant.contact_number || '',
          is_available:
            typeof resturant.is_available === 'boolean'
              ? resturant.is_available
              : true,
          start_time: resturant.start_time || '08:00',
          end_time: resturant.end_time || '23:00'
        }
      : {
          country_id: '',
          city_id: '',
          area_id: '',
          address_en: '',
          address_ar: '',
          address_tr: '',
          name_en: '',
          name_ar: '',
          name_tr: '',
          email: '',
          phone: '',
          logo: '',
          latitude: '',
          longitude: '',
          facebook_url: '',
          instagram_url: '',
          contact_number: '',
          is_available: true,
          start_time: '08:00',
          end_time: '23:00'
        }
  });

  const { mutate: createRestaurant, isPending: isCreating } =
    useCreateResturant();
  const { mutate: updateRestaurant, isPending: isUpdating } =
    useUpdateRestaurant();
  const { mutate: uploadImage, isPending: isUploading } = useUploadImage();
  const [previewImages, setPreviewImages] = useState<Record<string, string>>(
    {}
  );
  const { countries } = useFetchCountries();

  // Fetch cities based on selected country
  const { cities, isLoading: isLoadingCities } = useFetchCities(
    form.watch('country_id')
  );

  // Fetch areas based on selected city
  const { areas } = useFetchAreas(form.watch('city_id'));

  // Improved location data handling for restaurant updates
  useEffect(() => {
    if (resturant && resturant.country_id && countries?.length > 0) {
      const countryExists = countries.find((c: any) => c.id.toString() === resturant.country_id.toString());
      if (countryExists) {
        console.log('Setting country_id:', resturant.country_id.toString());
        form.setValue('country_id', resturant.country_id.toString(), {
          shouldValidate: true,
          shouldDirty: false
        });
      }
    }
  }, [resturant, countries, form]);

  // Set city when cities are loaded and restaurant has city_id
  useEffect(() => {
    if (
      resturant &&
      resturant.city_id &&
      !isLoadingCities &&
      cities?.length > 0 &&
      form.watch('country_id') === resturant.country_id.toString()
    ) {
      const cityExists = cities.find((c: any) => c.id.toString() === resturant.city_id.toString());
      if (cityExists) {
        console.log('Setting city_id:', resturant.city_id.toString());
        form.setValue('city_id', resturant.city_id.toString(), {
          shouldValidate: true,
          shouldDirty: false
        });
      }
    }
  }, [resturant, cities, isLoadingCities, form]);

  // Set area when areas are loaded and restaurant has area_id
  useEffect(() => {
    if (
      resturant &&
      resturant.area_id &&
      areas?.length > 0 &&
      form.watch('city_id') === resturant.city_id.toString()
    ) {
      const areaExists = areas.find((a: any) => a.id.toString() === resturant.area_id.toString());
      if (areaExists) {
        console.log('Setting area_id:', resturant.area_id.toString());
        form.setValue('area_id', resturant.area_id.toString(), {
          shouldValidate: true,
          shouldDirty: false
        });
      }
    }
  }, [resturant, areas, form]);

  const handleImageChange = (type: 'RESTAURANT_LOGO', file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    // Create a temporary preview URL for immediate feedback
    const tempPreviewUrl = URL.createObjectURL(file);
    setPreviewImages({ [type]: tempPreviewUrl });

    uploadImage(formData, {
      onSuccess: (response) => {
        try {
          // Revoke the temporary object URL to prevent memory leaks
          URL.revokeObjectURL(tempPreviewUrl);

          // Log the entire response to understand its structure
          console.log('Upload response data:', response);

          // Safely access the data structure
          if (response && response.data) {
            // Set the form value to the path returned from the server
            if (response.data.path) {
              form.setValue('logo', response.data.path, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true
              });
              console.log('Logo form value set to:', response.data.path);
            }

            // Use the complete URL from the server for the preview image
            // First try to get the URL, then fall back to path
            let serverImageUrl = '';
            if (response.data.url) {
              serverImageUrl = response.data.url;
            } else if (response.data.path) {
              serverImageUrl = response.data.path;
            }

            if (serverImageUrl) {
              setPreviewImages({ [type]: serverImageUrl });
              console.log('Image uploaded successfully:', serverImageUrl);
              console.log(
                'Current form values after logo upload:',
                form.getValues()
              );
              toast.success('Logo uploaded successfully');
            } else {
              console.error('No valid image URL found in response');
              setPreviewImages({ [type]: '' });
              toast.error('Failed to get image URL from server');
            }
          } else {
            console.error('Invalid response structure:', response);
            setPreviewImages({ [type]: '' });
            toast.error('Invalid response from server');
          }
        } catch (err) {
          console.error('Error processing upload response:', err);
          setPreviewImages({ [type]: '' });
          toast.error('Error processing upload response');
        }
      },
      onError: (error) => {
        console.error(`Error uploading ${type} image:`, error);
        // Revoke the temporary object URL on error
        URL.revokeObjectURL(tempPreviewUrl);
        setPreviewImages({ [type]: '' });
        toast.error('Failed to upload logo');
      }
    });
  };

  const handleRemoveImage = (type: string) => {
    if (previewImages[type]) {
      // Only revoke if it's a blob URL (from local file)
      if (previewImages[type].startsWith('blob:')) {
        URL.revokeObjectURL(previewImages[type]);
      }
    }
    setPreviewImages((prev) => ({ ...prev, [type]: '' }));
    form.setValue('logo', '');
  };

  const onSubmit = async (data: RestaurantFormValues) => {
    try {
      console.log('Form data being submitted:', data);

      // Check for validation errors before submitting
      const formErrors = form.formState.errors;
      if (Object.keys(formErrors).length > 0) {
        console.error('Form has validation errors:', formErrors);

        // Show specific error messages
        const errorMessages = Object.entries(formErrors).map(([field, error]) => {
          return `${field}: ${error?.message || 'Invalid value'}`;
        });

        toast.error(`Please fix the following errors:\n${errorMessages.join('\n')}`);

        // Focus on the first error field
        const firstErrorField = Object.keys(formErrors)[0];
        const element = document.querySelector(`[name="${firstErrorField}"]`) as HTMLElement;
        if (element) {
          element.focus();
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        return; // Stop submission
      }

      // Additional validation checks
      if (!data.country_id || !data.city_id || !data.area_id) {
        toast.error('Please select country, city, and area');
        return;
      }

      if (!data.latitude || !data.longitude) {
        toast.error('Please select a location on the map');
        return;
      }

      // Validate coordinates
      const lat = parseFloat(data.latitude);
      const lng = parseFloat(data.longitude);
      if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
        toast.error('Invalid coordinates. Please select a valid location on the map');
        return;
      }

      console.log('Logo value in form data:', data.logo);

      if (resturant) {
        console.log('Updating restaurant with ID:', resturant.id);
        console.log('Original restaurant data:', {
          country_id: resturant.country_id,
          city_id: resturant.city_id,
          area_id: resturant.area_id
        });

        // Create a clean copy of the data
        const updateData = { ...data };
        console.log('Update data being sent:', updateData);

        updateRestaurant(
          { id: resturant.id, data: updateData },
          {
            onSuccess: (response) => {
              console.log('Restaurant updated successfully:', response);
              toast.success('Restaurant updated successfully');
              modalClose();
            },
            onError: (error: any) => {
              console.error('Update error details:', error);
              const errorMessage = error?.response?.data?.message || error?.message || 'Failed to update restaurant';
              toast.error(errorMessage);
            }
          }
        );
      } else {
        console.log('Creating new restaurant with data:', data);
        createRestaurant(data, {
          onSuccess: (response) => {
            console.log('Restaurant created successfully:', response);
            toast.success('New restaurant added successfully');
            modalClose();
          },
          onError: (error: any) => {
            console.error('Create error details:', error);
            const errorMessage = error?.response?.data?.message || error?.message || 'Failed to create restaurant';
            toast.error(errorMessage);
          }
        });
      }
    } catch (err) {
      console.error('Error in form submission:', err);
      toast.error('An unexpected error occurred');
    }
  };

  // Set initial values when editing a restaurant
  useEffect(() => {
    try {
      // Set logo preview
      if (resturant?.logo) {
        let logoUrl = '';

        // Handle both object and string logo formats
        if (typeof resturant.logo === 'object' && resturant.logo?.url) {
          logoUrl = resturant.logo.url;
        } else if (typeof resturant.logo === 'string') {
          logoUrl = resturant.logo;
        }

        console.log('Setting initial logo preview:', logoUrl);

        // Make sure we're setting a valid URL
        if (logoUrl && logoUrl.trim() !== '') {
          setPreviewImages({ RESTAURANT_LOGO: logoUrl });
        } else {
          console.warn('Invalid logo URL in restaurant data:', resturant.logo);
        }
      }

      // Log the initial values for debugging
      if (resturant) {
        console.log('Initial form values:', {
          country_id: resturant.country_id,
          city_id: resturant.city_id,
          area_id: resturant.area_id
        });
      }
    } catch (err) {
      console.error('Error setting initial values:', err);
    }
  }, [resturant]);

  return (
    <div className="max-h-[125vh]  p-4">
      <Heading
        title={resturant ? 'Update Restaurant' : 'Create New Restaurant'}
        description=""
        className="mb-6 text-center"
      />
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-6"
          autoComplete="off"
        >
          {/* Validation Error Summary */}
          {Object.keys(form.formState.errors).length > 0 && (
            <Card className="border-red-200 bg-red-50">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-red-800">
                  <X className="h-5 w-5" />
                  Please fix the following errors:
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-red-700">
                  {Object.entries(form.formState.errors).map(([field, error]) => (
                    <li key={field} className="flex items-center gap-2">
                      <span className="h-1 w-1 rounded-full bg-red-500" />
                      <span className="font-medium capitalize">{field.replace('_', ' ')}:</span>
                      <span>{error?.message}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
          {/* Location Information */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <MapPin className="h-5 w-5" />
                Location Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="country_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        <Globe className="h-4 w-4" />
                        {t('country')}
                      </FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          // Reset city and area when country changes
                          if (!resturant || value !== resturant.country_id.toString()) {
                            form.setValue('city_id', '');
                            form.setValue('area_id', '');
                          }
                        }}
                        value={field.value}
                        disabled={isCreating || isUpdating}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('selectCountry')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {countries?.map((country: any) => (
                            <SelectItem
                              key={country.id}
                              value={country.id.toString()}
                            >
                              {country.name_en}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="city_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        <Building2 className="h-4 w-4" />
                        {t('city')}
                      </FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          // Reset area when city changes
                          if (!resturant || value !== resturant.city_id.toString()) {
                            form.setValue('area_id', '');
                          }
                        }}
                        value={field.value}
                        disabled={
                          isCreating || isUpdating || !form.watch('country_id')
                        }
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('selectCity')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {cities?.map((city: any) => (
                            <SelectItem key={city.id} value={city.id.toString()}>
                              {city.name_en}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="area_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        {t('area')}
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={
                          isCreating || isUpdating || !form.watch('city_id')
                        }
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('selectArea')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {areas?.map((area: any) => (
                            <SelectItem key={area.id} value={area.id.toString()}>
                              {area.name_en}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Address Fields */}
              <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-3">

                <FormField
                  control={form.control}
                  name="address_en"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('address')} (English)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Address in English"
                          {...field}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address_ar"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('address')} (Arabic)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="العنوان بالعربية"
                          {...field}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address_tr"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('address')} (Turkish)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Türkçe adres"
                          {...field}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Restaurant Information */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Building2 className="h-5 w-5" />
                Restaurant Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">

                <FormField
                  control={form.control}
                  name="name_en"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('name')} (English)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Restaurant name in English"
                          {...field}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="name_ar"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('name')} (Arabic)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="اسم المطعم بالعربية"
                          {...field}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="name_tr"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('name')} (Turkish)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Türkçe restoran adı"
                          {...field}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Phone className="h-5 w-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        <Mail className="h-4 w-4" />
                        {t('email')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          type="email"
                          {...field}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        <Phone className="h-4 w-4" />
                        {t('phone')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="+1234567890"
                          {...field}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contact_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        <Phone className="h-4 w-4" />
                        {t('contactNumber')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="+1234567890"
                          {...field}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Logo Upload */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <ImageIcon className="h-5 w-5" />
                Restaurant Logo
              </CardTitle>
            </CardHeader>
            <CardContent>

              <FormField
                control={form.control}
                name="logo"
                render={() => (
                  <FormItem>
                    <FormLabel>Upload Logo</FormLabel>
                    <div className="flex items-center gap-4">
                      {previewImages.RESTAURANT_LOGO ? (
                        <div className="relative">
                          <img
                            src={previewImages.RESTAURANT_LOGO}
                            alt="Logo preview"
                            className="h-24 w-24 rounded-lg object-cover border-2 border-gray-200"
                          />
                          <button
                            type="button"
                            onClick={() => handleRemoveImage('RESTAURANT_LOGO')}
                            className="absolute -right-2 -top-2 rounded-full bg-red-500 p-1 text-white shadow-sm hover:bg-red-600"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </div>
                      ) : (
                        <div className="flex h-24 w-24 items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50">
                          <ImageIcon className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                      <div className="flex-1">
                        <FormControl>
                          <Input
                            type="file"
                            accept="image/*"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                handleImageChange('RESTAURANT_LOGO', file);
                              }
                            }}
                            disabled={isUploading || isCreating || isUpdating}
                            className="cursor-pointer"
                          />
                        </FormControl>
                        <p className="mt-1 text-sm text-gray-500">
                          Upload a logo for your restaurant (PNG, JPG, GIF up to 10MB)
                        </p>
                      </div>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Map Location */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <MapPin className="h-5 w-5" />
                Restaurant Location
              </CardTitle>
            </CardHeader>
            <CardContent>

              <div className="space-y-4">
                <p className="text-sm text-gray-600">
                  Click on the map to set the restaurant's exact location
                </p>
                <div className="w-full">
                  {!isCreating && !isUpdating ? (
                    <LocationPicker
                      initialLat={form.watch('latitude') || '33.5138'}
                      initialLng={form.watch('longitude') || '36.2765'}
                      onLocationSelect={(lat, lng) => {
                        form.setValue('latitude', lat.toString(), {
                          shouldValidate: true,
                          shouldDirty: true
                        });
                        form.setValue('longitude', lng.toString(), {
                          shouldValidate: true,
                          shouldDirty: true
                        });
                        console.log('Location updated:', { lat, lng });
                      }}
                    />
                  ) : (
                    <div className="flex h-80 w-full items-center justify-center rounded-md bg-gray-100">
                      <p className="text-muted-foreground">
                        Map loading is disabled while form is submitting...
                      </p>
                    </div>
                  )}
                </div>

                {/* Show current coordinates */}
                {form.watch('latitude') && form.watch('longitude') && (
                  <div className="rounded-lg bg-gray-50 p-3">
                    <p className="text-sm font-medium text-gray-700">Current Location:</p>
                    <p className="text-sm text-gray-600">
                      Latitude: {form.watch('latitude')}, Longitude: {form.watch('longitude')}
                    </p>
                  </div>
                )}

                {/* Hidden fields for latitude and longitude */}
                <div className="hidden">
                  <FormField
                    control={form.control}
                    name="latitude"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="longitude"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Social Media & Settings */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <ToggleLeft className="h-5 w-5" />
                Additional Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Social Media */}
                <FormField
                  control={form.control}
                  name="facebook_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        <Share2 className="h-4 w-4" />
                        {t('facebookUrl')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="https://facebook.com/restaurant"
                          {...field}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="instagram_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        <Camera className="h-4 w-4" />
                        {t('instagramUrl')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="https://instagram.com/restaurant"
                          {...field}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Availability */}
                <FormField
                  control={form.control}
                  name="is_available"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 md:col-span-2">
                      <div className="space-y-0.5">
                        <FormLabel className="flex items-center gap-2 text-base">
                          <ToggleLeft className="h-4 w-4" />
                          {t('available')}
                        </FormLabel>
                        <p className="text-sm text-gray-500">
                          Enable this to make the restaurant available for orders
                        </p>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              {/* Working Hours */}
              <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="start_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {t('openingTime')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="time"
                          {...field}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="end_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {t('closingTime')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="time"
                          {...field}
                          disabled={isCreating || isUpdating}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Debug Information (Development Only) */}
          {process.env.NODE_ENV === 'development' && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm text-yellow-800">Debug Information</CardTitle>
              </CardHeader>
              <CardContent className="text-xs">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="font-medium">Form State:</p>
                    <p>Country: {form.watch('country_id') || 'Not selected'}</p>
                    <p>City: {form.watch('city_id') || 'Not selected'}</p>
                    <p>Area: {form.watch('area_id') || 'Not selected'}</p>
                    <p>Latitude: {form.watch('latitude') || 'Not set'}</p>
                    <p>Longitude: {form.watch('longitude') || 'Not set'}</p>
                  </div>
                  <div>
                    <p className="font-medium">Restaurant Data:</p>
                    {resturant ? (
                      <>
                        <p>Original Country: {resturant.country_id}</p>
                        <p>Original City: {resturant.city_id}</p>
                        <p>Original Area: {resturant.area_id}</p>
                      </>
                    ) : (
                      <p>Creating new restaurant</p>
                    )}
                  </div>
                </div>
                <div className="mt-2">
                  <p className="font-medium">Available Options:</p>
                  <p>Countries: {countries?.length || 0}</p>
                  <p>Cities: {cities?.length || 0}</p>
                  <p>Areas: {areas?.length || 0}</p>
                </div>
                {Object.keys(form.formState.errors).length > 0 && (
                  <div className="mt-2">
                    <p className="font-medium text-red-600">Form Errors:</p>
                    {Object.entries(form.formState.errors).map(([field, error]) => (
                      <p key={field} className="text-red-600">
                        {field}: {error?.message}
                      </p>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-end gap-4 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={modalClose}
              disabled={isCreating || isUpdating}
              className="min-w-[120px]"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isCreating || isUpdating}
              className="min-w-[120px]"
            >
              {isCreating || isUpdating ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  {resturant ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                resturant ? 'Update Restaurant' : 'Create Restaurant'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default RestaurantForm;
